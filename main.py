# coding=utf-8
from __future__ import print_function, absolute_import
import numpy as np
import pandas as pd
from gm.api import *

'''
shape_skew_m 单因子策略
基于分钟收益率偏度的高频因子策略
策略逻辑：偏度越低的股票，未来收益越好（反转特征）
'''

def init(context):
    # 每月第一个交易日09:40执行选股
    schedule(schedule_func=rebalance, date_rule='1m', time_rule='09:40:00')

    # 策略参数设置
    context.stock_num = 20  # 持仓股票数量
    context.lookback_days = 20  # 计算因子的历史天数
    context.position_ratio = 0.95  # 仓位比例

    print("shape_skew_m单因子策略初始化完成")


def calculate_shape_skew_factor(symbol, end_date, lookback_days=20):
    """
    计算shape_skew_m因子
    基于分钟收益率偏度的高频因子
    """
    try:
        # 获取分钟K线数据
        total_minutes = lookback_days * 240
        minute_data = history_n(
            symbol=symbol,
            frequency='60s',
            count=total_minutes,
            end_time=end_date,
            fields='close',
            skip_suspended=True,
            fill_missing='Last',
            adjust=ADJUST_PREV,
            df=True
        )

        if minute_data is None or len(minute_data) < 100:
            return np.nan

        # 计算分钟收益率
        minute_data['returns'] = minute_data['close'].pct_change()
        minute_data = minute_data.dropna()

        if len(minute_data) < 50:
            return np.nan

        # 按交易日分组计算每日偏度
        minute_data['date'] = pd.to_datetime(minute_data.index).strftime('%Y-%m-%d')
        daily_skews = []

        for date, group in minute_data.groupby('date'):
            if len(group) >= 10:
                daily_skew = group['returns'].skew()
                if not np.isnan(daily_skew):
                    daily_skews.append(daily_skew)

        if len(daily_skews) < 5:
            return np.nan

        # 计算偏度均值 (shape_skew_m)
        factor_value = np.mean(daily_skews)
        return factor_value

    except Exception as e:
        print(f"计算{symbol}因子时出错: {str(e)}")
        return np.nan


def get_stock_pool():
    """获取全市场股票池"""
    try:
        print("🔄 正在获取全市场股票池...")

        # 获取沪深两市所有A股
        print("  📊 获取沪深两市所有A股...")

        try:
            print("    🔄 尝试获取沪深两市A股...")
            # 获取沪市A股
            sh_stocks = get_instruments(exchanges='SHSE', sec_types=1, df=True)
            # 获取深市A股
            sz_stocks = get_instruments(exchanges='SZSE', sec_types=1, df=True)

            print(f"    📊 沪市股票获取结果: {len(sh_stocks) if sh_stocks is not None and not sh_stocks.empty else 0}只")
            print(f"    📊 深市股票获取结果: {len(sz_stocks) if sz_stocks is not None and not sz_stocks.empty else 0}只")

            if sh_stocks is not None and not sh_stocks.empty and sz_stocks is not None and not sz_stocks.empty:
                # 合并沪深股票
                all_stocks = pd.concat([sh_stocks, sz_stocks], ignore_index=True)
                print(f"    📊 合并后总股票数: {len(all_stocks)}只")

                # 过滤掉ST、*ST等特殊股票
                filtered_stocks = all_stocks[
                    (~all_stocks['symbol'].str.contains('ST', na=False)) &
                    (~all_stocks['symbol'].str.contains('退', na=False)) &
                    (all_stocks['symbol'].str.len() >= 11)  # 确保是完整的股票代码格式
                ]

                stock_pool = list(filtered_stocks['symbol'])
                print(f"    ✅ 成功获取沪深两市A股: {len(stock_pool)}只")
                print(f"      沪市股票: {len([s for s in stock_pool if s.startswith('SHSE')])}只")
                print(f"      深市股票: {len([s for s in stock_pool if s.startswith('SZSE')])}只")
                return stock_pool
            else:
                raise Exception(f"股票数据获取失败")

        except Exception as e:
            print(f"    ❌ 获取全市场股票失败: {e}")
            # 使用扩展的备用股票池
            print("    🔄 使用扩展备用股票池")
            stock_pool = [
                # 沪市主要股票
                'SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SHSE.600276', 'SHSE.600000',
                'SHSE.600887', 'SHSE.601166', 'SHSE.601288', 'SHSE.600585', 'SHSE.601398',
                'SHSE.601857', 'SHSE.601012', 'SHSE.600309', 'SHSE.601668', 'SHSE.600104',
                'SHSE.600030', 'SHSE.600048', 'SHSE.600196', 'SHSE.600690', 'SHSE.600703',
                # 深市主要股票
                'SZSE.000002', 'SZSE.000001', 'SZSE.002415', 'SZSE.000858', 'SZSE.002594',
                'SZSE.000725', 'SZSE.002230', 'SZSE.000063', 'SZSE.002352', 'SZSE.000876',
                'SZSE.002142', 'SZSE.000568', 'SZSE.002475', 'SZSE.000895', 'SZSE.002304',
                'SZSE.300059', 'SZSE.300015', 'SZSE.300750', 'SZSE.300760', 'SZSE.300896'
            ]
            print(f"    ⚠️ 使用扩展备用股票池: {len(stock_pool)}只")
            return stock_pool

    except Exception as e:
        print(f"❌ 获取股票池失败: {e}")
        # 最小核心股票池
        stock_pool = [
            'SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001'
        ]
        print(f"🆘 使用最小核心股票池: {len(stock_pool)}只")
        return stock_pool


def rebalance(context):
    """调仓函数"""
    print(f"\n=== {context.now} 开始调仓 ===")

    # 获取全市场股票池
    stock_pool = get_stock_pool()
    print(f"股票池规模: {len(stock_pool)}只")

    # 计算所有股票的shape_skew_m因子
    last_day = get_previous_trading_date(exchange='SHSE', date=context.now)
    factor_data = {}

    print("开始计算因子...")

    # 分批处理，避免一次性处理过多股票
    batch_size = 100  # 每批处理100只股票
    total_batches = (len(stock_pool) + batch_size - 1) // batch_size

    for batch_idx in range(total_batches):
        start_idx = batch_idx * batch_size
        end_idx = min((batch_idx + 1) * batch_size, len(stock_pool))
        batch_symbols = stock_pool[start_idx:end_idx]

        print(f"处理第{batch_idx + 1}/{total_batches}批: {len(batch_symbols)}只股票")

        for symbol in batch_symbols:
            try:
                factor_value = calculate_shape_skew_factor(
                    symbol,
                    last_day,
                    context.lookback_days
                )

                if not np.isnan(factor_value):
                    factor_data[symbol] = factor_value

            except Exception as e:
                print(f"计算{symbol}因子失败: {str(e)}")
                continue

        # 每批处理后显示进度
        print(f"第{batch_idx + 1}批完成，累计有效因子: {len(factor_data)}只")

    print(f"因子计算完成，有效因子数量: {len(factor_data)}只")

    if len(factor_data) < 5:
        print(f"有效因子数量不足({len(factor_data)}只)，跳过本次调仓")
        return

    # 因子排序选股 (偏度越小越好)
    sorted_stocks = sorted(factor_data.items(), key=lambda x: x[1])
    actual_stock_num = min(context.stock_num, len(sorted_stocks))
    selected_stocks = [stock[0] for stock in sorted_stocks[:actual_stock_num]]

    print(f"选出的股票: {selected_stocks[:5]}...")
    print(f"对应因子值: {[round(factor_data[s], 4) for s in selected_stocks[:5]]}")

    # 获取当前持仓
    current_positions = context.account().positions()
    current_symbols = [pos['symbol'] for pos in current_positions]

    # 平掉不在新选股中的持仓
    for symbol in current_symbols:
        if symbol not in selected_stocks:
            order_target_percent(
                symbol=symbol,
                percent=0,
                order_type=OrderType_Market,
                position_side=PositionSide_Long
            )
            print(f"平仓: {symbol}")

    # 等权重买入选中的股票
    target_percent = context.position_ratio / len(selected_stocks)

    for symbol in selected_stocks:
        order_target_percent(
            symbol=symbol,
            percent=target_percent,
            order_type=OrderType_Market,
            position_side=PositionSide_Long
        )
        print(f"买入: {symbol}, 目标仓位: {target_percent:.2%}")

    print("=== 调仓完成 ===\n")


def on_backtest_finished(context, indicator):
    """回测结束后打印绩效指标"""
    print("\n" + "="*50)
    print("shape_skew_m单因子策略回测结果:")
    print("="*50)

    # 打印主要绩效指标
    try:
        for key, value in indicator.items():
            if isinstance(value, (int, float)):
                if 'ratio' in key or 'return' in key:
                    print(f"{key}: {value:.2%}")
                else:
                    print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    except Exception as e:
        print(f"打印指标时出错: {e}")
        print("原始指标数据:", indicator)

    print("="*50)


if __name__ == '__main__':
    '''
    shape_skew_m单因子策略
    - 基于分钟收益率偏度的高频因子
    - 每月调仓一次，选择偏度最小的20只股票
    - 等权重配置，总仓位95%
    '''
    run(strategy_id='2e8defcd-7040-11f0-9d04-00e2699251ab',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='787cec949649eb140ea4ccccb14523af76110c8f',
        backtest_start_time='2021-01-01 08:00:00',
        backtest_end_time='2023-12-31 16:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=10000000,
        backtest_commission_ratio=0.0001,
        backtest_slippage_ratio=0.0001,
        backtest_match_mode=1)