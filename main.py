# coding=utf-8
from __future__ import print_function, absolute_import
import numpy as np
import pandas as pd
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from gm.api import *

'''
shape_skew_m 单因子策略
基于分钟收益率偏度的高频因子策略
策略逻辑：偏度越低的股票，未来收益越好（反转特征）
'''

def init(context):
    # 每月第一个交易日09:40执行选股
    schedule(schedule_func=rebalance, date_rule='1m', time_rule='09:40:00')

    # 策略参数设置
    context.stock_num = 20  # 持仓股票数量
    context.lookback_days = 20  # 计算因子的历史天数
    context.position_ratio = 0.95  # 仓位比例

    # 优化参数
    context.max_workers = 8  # 并行线程数
    context.pre_filter_size = 800  # 预筛选股票数量
    context.batch_size = 20  # 批量处理大小

    # 缓存机制
    context.factor_cache = {}  # 因子缓存
    context.cache_date = None  # 缓存日期
    context.basic_data_cache = {}  # 基础数据缓存

    print("shape_skew_m单因子策略初始化完成（已启用并行计算和缓存机制）")


def pre_filter_stocks(symbols, end_date, target_size=800):
    """预筛选机制：先用简单条件筛选股票"""
    print(f"开始预筛选，从{len(symbols)}只股票中筛选{target_size}只...")

    filtered_stocks = []

    try:
        # 批量获取基础数据进行预筛选
        batch_size = 100
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]

            try:
                # 获取最近1天的基础数据用于预筛选
                basic_data = history_n(
                    symbol=batch_symbols,
                    frequency='1d',
                    count=2,  # 获取2天数据计算涨跌幅
                    end_time=end_date,
                    fields='close,volume,amount',
                    skip_suspended=True,
                    df=True
                )

                if basic_data is not None and not basic_data.empty:
                    # 按股票分组处理
                    for symbol in batch_symbols:
                        symbol_data = basic_data[basic_data.index.get_level_values(0) == symbol] if hasattr(basic_data.index, 'get_level_values') else basic_data

                        if len(symbol_data) >= 2:
                            # 计算基础指标
                            recent_volume = symbol_data['volume'].iloc[-1]
                            recent_amount = symbol_data['amount'].iloc[-1]

                            # 预筛选条件：成交量和成交额不为0
                            if recent_volume > 0 and recent_amount > 0:
                                filtered_stocks.append(symbol)

            except Exception as e:
                print(f"预筛选批次{i//batch_size + 1}失败: {e}")
                # 如果批量获取失败，直接添加这批股票
                filtered_stocks.extend(batch_symbols)
                continue

        # 如果筛选结果太少，补充原始股票
        if len(filtered_stocks) < target_size:
            remaining = target_size - len(filtered_stocks)
            for symbol in symbols:
                if symbol not in filtered_stocks:
                    filtered_stocks.append(symbol)
                    remaining -= 1
                    if remaining <= 0:
                        break

        # 限制最终数量
        filtered_stocks = filtered_stocks[:target_size]

    except Exception as e:
        print(f"预筛选失败，使用原始股票池前{target_size}只: {e}")
        filtered_stocks = symbols[:target_size]

    print(f"预筛选完成，筛选出{len(filtered_stocks)}只股票")
    return filtered_stocks


def calculate_shape_skew_factor(symbol, end_date, lookback_days=20, debug=False):
    """
    计算shape_skew_m因子（改进版本，增加调试信息）
    基于分钟收益率偏度的高频因子
    """
    try:
        if debug:
            print(f"  开始计算{symbol}的shape_skew_m因子...")

        # 获取分钟K线数据
        total_minutes = lookback_days * 240
        minute_data = history_n(
            symbol=symbol,
            frequency='60s',
            count=total_minutes,
            end_time=end_date,
            fields='close',
            skip_suspended=True,
            fill_missing='Last',
            adjust=ADJUST_PREV,
            df=True
        )

        if minute_data is None:
            if debug:
                print(f"  {symbol}: 分钟数据获取失败(None)")
            return np.nan

        if len(minute_data) < 100:
            if debug:
                print(f"  {symbol}: 分钟数据不足({len(minute_data)}条，需要至少100条)")
            return np.nan

        if debug:
            print(f"  {symbol}: 获取到{len(minute_data)}条分钟数据")

        # 计算分钟收益率
        minute_data['returns'] = minute_data['close'].pct_change()
        minute_data = minute_data.dropna()

        if len(minute_data) < 50:
            if debug:
                print(f"  {symbol}: 有效收益率数据不足({len(minute_data)}条)")
            return np.nan

        # 按交易日分组计算每日偏度
        try:
            minute_data['date'] = minute_data.index.to_series().dt.strftime('%Y-%m-%d')
        except:
            # 备用方法
            minute_data['date'] = pd.to_datetime(minute_data.index).strftime('%Y-%m-%d')

        daily_skews = []

        for date, group in minute_data.groupby('date'):
            if len(group) >= 10:  # 确保每日至少有10个分钟数据
                daily_skew = group['returns'].skew()
                if not np.isnan(daily_skew) and np.isfinite(daily_skew):
                    daily_skews.append(daily_skew)
                    if debug:
                        print(f"  {symbol} {date}: 偏度={daily_skew:.4f} (数据点:{len(group)})")

        if len(daily_skews) < 5:
            if debug:
                print(f"  {symbol}: 有效日偏度数据不足({len(daily_skews)}天，需要至少5天)")
            return np.nan

        # 计算偏度均值 (shape_skew_m)
        factor_value = np.mean(daily_skews)

        if debug:
            print(f"  {symbol}: shape_skew_m = {factor_value:.6f} (基于{len(daily_skews)}天数据)")

        return factor_value

    except Exception as e:
        if debug:
            print(f"  {symbol}: 计算异常 - {str(e)}")
        return np.nan


def calculate_shape_skew_factor_cached(context, symbol, end_date, lookback_days=20):
    """带缓存的因子计算"""
    cache_key = f"{symbol}_{end_date}_{lookback_days}"

    # 检查缓存
    if (hasattr(context, 'factor_cache') and
        context.cache_date == end_date and
        cache_key in context.factor_cache):
        return context.factor_cache[cache_key]

    # 计算因子
    factor_value = calculate_shape_skew_factor(symbol, end_date, lookback_days)

    # 存入缓存
    if not hasattr(context, 'factor_cache'):
        context.factor_cache = {}
    context.factor_cache[cache_key] = factor_value
    context.cache_date = end_date

    return factor_value


def parallel_calculate_factors(context, symbols, end_date, lookback_days=20):
    """并行计算因子"""
    print(f"开始并行计算{len(symbols)}只股票的因子...")

    factor_data = {}
    lock = threading.Lock()

    def calculate_single_factor(symbol):
        try:
            factor_value = calculate_shape_skew_factor_cached(
                context, symbol, end_date, lookback_days
            )
            if not np.isnan(factor_value):
                with lock:
                    factor_data[symbol] = factor_value
                    if len(factor_data) % 50 == 0:  # 每50个打印一次进度
                        print(f"  已完成{len(factor_data)}只股票的因子计算...")
        except Exception as e:
            print(f"  {symbol}计算失败: {e}")

    # 使用线程池并行计算
    max_workers = getattr(context, 'max_workers', 8)
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        futures = [executor.submit(calculate_single_factor, symbol) for symbol in symbols]

        # 等待所有任务完成
        for future in as_completed(futures):
            try:
                future.result()  # 获取结果，如果有异常会抛出
            except Exception as e:
                print(f"  线程执行异常: {e}")

    print(f"并行计算完成，有效因子数量: {len(factor_data)}只")
    return factor_data


def get_stock_pool():
    """获取全市场股票池"""
    try:
        print("🔄 正在获取全市场股票池...")

        # 获取沪深两市所有A股
        print("  📊 获取沪深两市所有A股...")

        try:
            print("    🔄 尝试获取沪深两市A股...")
            # 获取沪市A股
            sh_stocks = get_instruments(exchanges='SHSE', sec_types=1, df=True)
            # 获取深市A股
            sz_stocks = get_instruments(exchanges='SZSE', sec_types=1, df=True)

            print(f"    📊 沪市股票获取结果: {len(sh_stocks) if sh_stocks is not None and not sh_stocks.empty else 0}只")
            print(f"    📊 深市股票获取结果: {len(sz_stocks) if sz_stocks is not None and not sz_stocks.empty else 0}只")

            if sh_stocks is not None and not sh_stocks.empty and sz_stocks is not None and not sz_stocks.empty:
                # 合并沪深股票
                all_stocks = pd.concat([sh_stocks, sz_stocks], ignore_index=True)
                print(f"    📊 合并后总股票数: {len(all_stocks)}只")

                # 过滤掉ST、*ST等特殊股票
                filtered_stocks = all_stocks[
                    (~all_stocks['symbol'].str.contains('ST', na=False)) &
                    (~all_stocks['symbol'].str.contains('退', na=False)) &
                    (all_stocks['symbol'].str.len() >= 11)  # 确保是完整的股票代码格式
                ]

                stock_pool = list(filtered_stocks['symbol'])
                print(f"    ✅ 成功获取沪深两市A股: {len(stock_pool)}只")
                print(f"      沪市股票: {len([s for s in stock_pool if s.startswith('SHSE')])}只")
                print(f"      深市股票: {len([s for s in stock_pool if s.startswith('SZSE')])}只")
                return stock_pool
            else:
                raise Exception(f"股票数据获取失败")

        except Exception as e:
            print(f"    ❌ 获取全市场股票失败: {e}")
            # 使用扩展的备用股票池
            print("    🔄 使用扩展备用股票池")
            stock_pool = [
                # 沪市主要股票
                'SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SHSE.600276', 'SHSE.600000',
                'SHSE.600887', 'SHSE.601166', 'SHSE.601288', 'SHSE.600585', 'SHSE.601398',
                'SHSE.601857', 'SHSE.601012', 'SHSE.600309', 'SHSE.601668', 'SHSE.600104',
                'SHSE.600030', 'SHSE.600048', 'SHSE.600196', 'SHSE.600690', 'SHSE.600703',
                # 深市主要股票
                'SZSE.000002', 'SZSE.000001', 'SZSE.002415', 'SZSE.000858', 'SZSE.002594',
                'SZSE.000725', 'SZSE.002230', 'SZSE.000063', 'SZSE.002352', 'SZSE.000876',
                'SZSE.002142', 'SZSE.000568', 'SZSE.002475', 'SZSE.000895', 'SZSE.002304',
                'SZSE.300059', 'SZSE.300015', 'SZSE.300750', 'SZSE.300760', 'SZSE.300896'
            ]
            print(f"    ⚠️ 使用扩展备用股票池: {len(stock_pool)}只")
            return stock_pool

    except Exception as e:
        print(f"❌ 获取股票池失败: {e}")
        # 最小核心股票池
        stock_pool = [
            'SHSE.600036', 'SHSE.601318', 'SHSE.600519', 'SZSE.000002', 'SZSE.000001'
        ]
        print(f"🆘 使用最小核心股票池: {len(stock_pool)}只")
        return stock_pool


def rebalance(context):
    """调仓函数（优化版本）"""
    print(f"\n=== {context.now} 开始调仓（优化版本） ===")

    # 获取全市场股票池
    stock_pool = get_stock_pool()
    print(f"原始股票池规模: {len(stock_pool)}只")

    # 预筛选机制：减少计算量
    pre_filter_size = getattr(context, 'pre_filter_size', 800)
    filtered_pool = pre_filter_stocks(stock_pool, context.now, pre_filter_size)
    print(f"预筛选后股票池: {len(filtered_pool)}只")

    # 计算所有股票的shape_skew_m因子
    last_day = get_previous_trading_date(exchange='SHSE', date=context.now)

    # 使用并行计算
    print("开始并行计算因子...")
    factor_data = parallel_calculate_factors(
        context,
        filtered_pool,
        last_day,
        context.lookback_days
    )

    print(f"因子计算完成，有效因子数量: {len(factor_data)}只")

    # 调试：如果有效因子为0，进行详细诊断
    if len(factor_data) == 0:
        print("\n⚠️ 有效因子数量为0，开始诊断...")

        # 随机选择5只股票进行详细调试
        test_symbols = filtered_pool[:5]
        print(f"对前5只股票进行详细调试: {test_symbols}")

        for symbol in test_symbols:
            print(f"\n--- 调试 {symbol} ---")
            factor_value = calculate_shape_skew_factor(
                symbol, last_day, context.lookback_days, debug=True
            )
            print(f"{symbol} 最终因子值: {factor_value}")

        print("跳过本次调仓，等待下次调仓时重试")
        return

    if len(factor_data) < 5:
        print(f"有效因子数量不足({len(factor_data)}只)，跳过本次调仓")
        return

    # 因子排序选股 (偏度越小越好)
    sorted_stocks = sorted(factor_data.items(), key=lambda x: x[1])
    actual_stock_num = min(context.stock_num, len(sorted_stocks))
    selected_stocks = [stock[0] for stock in sorted_stocks[:actual_stock_num]]

    print(f"选出的股票: {selected_stocks[:5]}...")
    print(f"对应因子值: {[round(factor_data[s], 4) for s in selected_stocks[:5]]}")

    # 获取当前持仓
    current_positions = context.account().positions()
    current_symbols = [pos['symbol'] for pos in current_positions]

    # 平掉不在新选股中的持仓
    for symbol in current_symbols:
        if symbol not in selected_stocks:
            order_target_percent(
                symbol=symbol,
                percent=0,
                order_type=OrderType_Market,
                position_side=PositionSide_Long
            )
            print(f"平仓: {symbol}")

    # 等权重买入选中的股票
    target_percent = context.position_ratio / len(selected_stocks)

    for symbol in selected_stocks:
        order_target_percent(
            symbol=symbol,
            percent=target_percent,
            order_type=OrderType_Market,
            position_side=PositionSide_Long
        )
        print(f"买入: {symbol}, 目标仓位: {target_percent:.2%}")

    print("=== 调仓完成 ===\n")


def on_backtest_finished(context, indicator):
    """回测结束后打印绩效指标"""
    print("\n" + "="*50)
    print("shape_skew_m单因子策略回测结果:")
    print("="*50)

    # 打印主要绩效指标
    try:
        for key, value in indicator.items():
            if isinstance(value, (int, float)):
                if 'ratio' in key or 'return' in key:
                    print(f"{key}: {value:.2%}")
                else:
                    print(f"{key}: {value:.4f}")
            else:
                print(f"{key}: {value}")
    except Exception as e:
        print(f"打印指标时出错: {e}")
        print("原始指标数据:", indicator)

    print("="*50)


if __name__ == '__main__':
    '''
    shape_skew_m单因子策略
    - 基于分钟收益率偏度的高频因子
    - 每月调仓一次，选择偏度最小的20只股票
    - 等权重配置，总仓位95%
    '''
    run(strategy_id='2e8defcd-7040-11f0-9d04-00e2699251ab',
        filename='main.py',
        mode=MODE_BACKTEST,
        token='787cec949649eb140ea4ccccb14523af76110c8f',
        backtest_start_time='2021-01-01 08:00:00',
        backtest_end_time='2023-12-31 16:00:00',
        backtest_adjust=ADJUST_PREV,
        backtest_initial_cash=10000000,
        backtest_commission_ratio=0.0001,
        backtest_slippage_ratio=0.0001,
        backtest_match_mode=1)