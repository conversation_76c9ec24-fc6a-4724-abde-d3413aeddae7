# 常用模式和最佳实践

- 掘金SDK因子挖掘关键API：get_fundamentals()获取基本面数据，get_history_constituents()获取成分股，get_history_instruments()获取交易标的信息。多因子模型包括Fama-French三因子（市场、规模、价值）、五因子（加盈利、投资）等。因子构建步骤：数据获取→因子分组→因子计算→回归分析→选股
- 高频因子挖掘8大类：1)动量反转(QRS、分段动量、量能动量) 2)波动性(上行/下行波动率) 3)高阶特征(偏度峰度) 4)流动性(Amihud、价差深度) 5)量价相关性(同步/领先滞后) 6)筹码分布(形状/占比) 7)拥挤度(傅里叶变换机构参与度) 8)资金成交(开盘尾盘、聪明资金流向)。高频因子优势：信息丰富、拥挤度低、相关性低
- shape_skew_m单因子策略实现：使用分钟K线计算日内收益率偏度，按日分组计算偏度后取均值。策略逻辑为偏度越小的股票未来收益越好（反转特征）。关键函数：calculate_shape_skew_factor()计算因子，rebalance()月度调仓选股，使用沪深300成分股池，等权重配置前20只低偏度股票
- 掘金SDK实盘策略优化技巧：1)使用@retry装饰器增强数据获取稳定性 2)order_target_percent()精确控制仓位比例 3)实盘模式使用保护价格(price*1.01买入,price*0.99卖出) 4)添加交易时段检查 5)使用get_cash()获取资金信息 6)添加详细的日志记录和异常处理 7)区分回测和实盘模式的不同处理逻辑
- shape_skew_m策略最终版本：使用固定股票池避免API兼容性问题，采用pd.to_datetime处理日期分组，简化代码结构移除复杂的日志和重试机制，核心逻辑为分钟K线计算收益率偏度后按日分组取均值，选择偏度最小的股票进行等权重配置
- 全市场shape_skew_m策略升级：使用get_instruments()动态获取沪深两市所有A股，过滤ST股票，分批处理因子计算(每批100只)，包含三级容错机制(全市场→扩展备用池→最小核心池)，支持数千只股票的因子计算和选股
